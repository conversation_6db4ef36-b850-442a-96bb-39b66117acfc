'use client'

import { useState } from 'react'
import { UserList } from '@/components/users/UserList'
import { UserForm } from '@/components/users/UserForm'
import { User } from '@/lib/db'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function Home() {
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)

  const handleUserSelect = (user: User) => {
    setSelectedUser(user)
    setShowForm(true)
  }

  const handleFormSuccess = () => {
    setSelectedUser(null)
    setShowForm(false)
    setRefreshKey(prev => prev + 1)
  }

  const handleCancel = () => {
    setSelectedUser(null)
    setShowForm(false)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        <header className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Next.js 技术栈演示项目
          </h1>
          <p className="text-gray-600 text-lg">
            使用 Next.js + TypeScript + Tailwind CSS + shadcn/ui 构建的用户管理系统
          </p>
        </header>

        <Card className="mb-6">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>用户管理</CardTitle>
              </div>
              <Button 
                onClick={() => {
                  setShowForm(true)
                  setSelectedUser(null)
                }}
              >
                添加新用户
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            {showForm ? (
              <UserForm 
                user={selectedUser}
                onSuccess={handleFormSuccess}
                onCancel={handleCancel}
              />
            ) : (
              <UserList 
                onUserSelect={handleUserSelect}
                refresh={refreshKey > 0}
              />
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>技术栈说明</CardTitle>
          </CardHeader>
          <CardContent className="grid md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="font-semibold mb-2">前端技术栈：</h3>
              <ul className="space-y-1 text-gray-600">
                <li>• Next.js 15 - React全栈框架</li>
                <li>• TypeScript - 类型安全的JavaScript</li>
                <li>• React 19 - 现代UI库</li>
                <li>• Tailwind CSS v4 - 原子化CSS框架</li>
                <li>• shadcn/ui - 高质量组件库</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">后端技术栈：</h3>
              <ul className="space-y-1 text-gray-600">
                <li>• Next.js API Routes - 服务端API</li>
                <li>• 模拟SQLite - 实际项目使用真实数据库</li>
                <li>• TypeScript - 全链路类型安全</li>
                <li>• RESTful API - 标准接口设计</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}