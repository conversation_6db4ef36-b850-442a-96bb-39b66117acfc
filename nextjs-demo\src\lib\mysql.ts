import mysql, { ResultSetHeader } from 'mysql2/promise'

// MySQL连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '123123',
  database: 'nextjs_demo',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
}

// 创建连接池
const pool = mysql.createPool(dbConfig)

// 初始化数据库和表
export async function initializeDatabase() {
  try {
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123123'
    })

    // 创建数据库（如果不存在）
    await connection.execute('CREATE DATABASE IF NOT EXISTS nextjs_demo')
    await connection.end()

    // 使用数据库
    const dbConnection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123123',
      database: 'nextjs_demo'
    })

    // 创建用户表
    await dbConnection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    await dbConnection.end()
    console.log('数据库初始化成功')
  } catch (error) {
    console.error('数据库初始化失败:', error)
    throw error
  }
}

export default pool