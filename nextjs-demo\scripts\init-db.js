const mysql = require('mysql2/promise')

async function initDatabase() {
  try {
    // 连接MySQL服务器
    const connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: '123123'
    })

    console.log('连接到MySQL服务器...')

    // 创建数据库
    await connection.execute('CREATE DATABASE IF NOT EXISTS nextjs_demo')
    console.log('✓ 数据库创建成功')

    // 使用数据库
    await connection.execute('USE nextjs_demo')

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)
    console.log('✓ 用户表创建成功')

    // 插入测试数据
    try {
      await connection.execute(`
        INSERT IGNORE INTO users (name, email) VALUES 
        ('张三', '<PERSON><PERSON><PERSON>@example.com'),
        ('李四', '<EMAIL>'),
        ('王五', '<EMAIL>')
      `)
      console.log('✓ 测试数据插入成功')
    } catch (insertError) {
      console.log('测试数据已存在，跳过插入')
    }

    await connection.end()
    console.log('🎉 数据库初始化完成！')

  } catch (error) {
    console.error('数据库初始化失败:', error.message)
    process.exit(1)
  }
}

initDatabase()