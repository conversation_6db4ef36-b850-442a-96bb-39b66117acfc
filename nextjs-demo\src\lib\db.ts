import pool, { initializeDatabase } from './mysql'
import { ResultSetHeader } from 'mysql2'

export interface User {
  id: number;
  name: string;
  email: string;
  createdAt: string;
}

class MySQLDatabase {
  async init() {
    await initializeDatabase()
  }

  async getUsers(): Promise<User[]> {
    try {
      const [rows] = await pool.execute('SELECT id, name, email, created_at as createdAt FROM users ORDER BY created_at DESC')
      return rows as User[]
    } catch (error) {
      console.error('获取用户失败:', error)
      throw error
    }
  }

  async getUserById(id: number): Promise<User | null> {
    try {
      const [rows] = await pool.execute(
        'SELECT id, name, email, created_at as createdAt FROM users WHERE id = ?',
        [id]
      )
      const users = rows as User[]
      return users.length > 0 ? users[0] : null
    } catch (error) {
      console.error('获取用户失败:', error)
      throw error
    }
  }

  async createUser(userData: { name: string; email: string }): Promise<User> {
    try {
      const [result] = await pool.execute(
        'INSERT INTO users (name, email) VALUES (?, ?)',
        [userData.name, userData.email]
      )
      
      const insertId = (result as ResultSetHeader).insertId
      const newUser = await this.getUserById(insertId)
      if (!newUser) throw new Error('创建用户失败')
      return newUser
    } catch (error) {
      console.error('创建用户失败:', error)
      throw error
    }
  }

  async updateUser(id: number, updates: { name: string; email: string }): Promise<User | null> {
    try {
      await pool.execute(
        'UPDATE users SET name = ?, email = ? WHERE id = ?',
        [updates.name, updates.email, id]
      )
      
      return await this.getUserById(id)
    } catch (error) {
      console.error('更新用户失败:', error)
      throw error
    }
  }

  async deleteUser(id: number): Promise<boolean> {
    try {
      const [result] = await pool.execute(
        'DELETE FROM users WHERE id = ?',
        [id]
      )
      
      return (result as ResultSetHeader).affectedRows > 0
    } catch (error) {
      console.error('删除用户失败:', error)
      throw error
    }
  }
}

// 初始化数据库
const db = new MySQLDatabase()
db.init().catch(console.error)

export { db }