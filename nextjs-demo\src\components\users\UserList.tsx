'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { User } from '@/lib/db'

interface UserListProps {
  onUserSelect: (user: User) => void
  refresh: boolean
}

export function UserList({ onUserSelect, refresh }: UserListProps) {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchUsers()
  }, [refresh])

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users')
      const data = await response.json()
      
      // 处理可能的错误响应
      if (data.error) {
        console.error('API错误:', data.error)
        setUsers([])
        return
      }
      
      // 确保数据是数组
      if (Array.isArray(data)) {
        setUsers(data)
      } else {
        console.error('数据格式错误:', data)
        setUsers([])
      }
    } catch (error) {
      console.error('获取用户失败:', error)
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这个用户吗？')) {
      try {
        await fetch(`/api/users/${id}`, {
          method: 'DELETE'
        })
        fetchUsers()
      } catch (error) {
        console.error('删除用户失败:', error)
      }
    }
  }

  if (loading) {
    return <div className="text-center py-4">加载中...</div>
  }

  if (users.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500 mb-4">暂无用户数据</p>
        <p className="text-sm text-gray-400">请添加一些用户开始使用</p>
      </div>
    )
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {users.map(user => (
        <Card key={user.id}>
          <CardHeader>
            <CardTitle className="text-lg">{user.name}</CardTitle>
            <CardDescription>{user.email}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600">
              创建时间: {user.createdAt}
            </p>
          </CardContent>
          <CardHeader>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => onUserSelect(user)}
              >
                编辑
              </Button>
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => handleDelete(user.id)}
              >
                删除
              </Button>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  )
}