# Next.js + TypeScript + Tailwind CSS + shadcn/ui + MySQL 技术栈完整指南

## 📋 项目概览

这是一个完整的现代全栈应用演示项目，展示了如何使用以下技术栈构建用户管理系统：

- **Next.js 15** - React全栈框架
- **TypeScript** - 类型安全的JavaScript超集
- **Tailwind CSS v4** - 原子化CSS框架
- **shadcn/ui** - 高质量组件库
- **MySQL** - 关系型数据库
- **MySQL2** - MySQL Node.js驱动

## 🏗️ 项目创建过程详解

### 第一阶段：项目初始化

#### 1.1 创建Next.js项目
```bash
npx create-next-app@latest nextjs-demo --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
```

**技术选择解析：**
- `--typescript`: 启用TypeScript支持
- `--tailwind`: 集成Tailwind CSS v4
- `--app`: 使用App Router（现代Next.js路由）
- `--src-dir`: 源代码放在src目录
- `--import-alias "@/*"`: 设置路径别名，简化导入

#### 1.2 安装核心依赖
```bash
npm install mysql2 @radix-ui/react-slot lucide-react class-variance-authority clsx tailwind-merge
```

### 第二阶段：技术栈配置

#### 2.1 目录结构建立
```
src/
├── app/                    # Next.js App Router
│   ├── api/users/         # API路由
│   ├── globals.css        # Tailwind CSS全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页
├── components/            # 组件目录
│   ├── ui/               # shadcn/ui组件
│   └── users/            # 用户管理组件
└── lib/                  # 工具库
    ├── db.ts             # 数据库访问层
    ├── mysql.ts          # MySQL连接配置
    └── utils.ts          # 工具函数
```

#### 2.2 shadcn/ui组件配置
- 创建 `components.json` 配置文件
- 设置 `@/components` 和 `@/lib/utils` 路径别名
- 安装基础UI组件：Button, Card, Input

#### 2.3 Tailwind CSS v4配置
- 使用最新的v4语法和特性
- 配置深色模式支持
- 设置CSS变量和颜色主题

### 第三阶段：数据库集成

#### 3.1 MySQL数据库配置

**数据库连接配置** (`src/lib/mysql.ts`):
```typescript
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '123123',
  database: 'nextjs_demo',
  waitForConnections: true,
  connectionLimit: 10
}
```

**数据库初始化** (`scripts/init-db.js`):
```sql
CREATE DATABASE IF NOT EXISTS nextjs_demo;

CREATE TABLE IF NOT EXISTS users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### 3.2 数据访问层实现

**MySQLDatabase类** (`src/lib/db.ts`):
- 连接池管理
- CRUD操作封装
- 类型安全的接口定义
- 错误处理和日志记录

### 第四阶段：API路由开发

#### 4.1 RESTful API设计

**API端点：**
- `GET /api/users` - 获取所有用户
- `POST /api/users` - 创建用户
- `PUT /api/users/[id]` - 更新用户
- `DELETE /api/users/[id]` - 删除用户

**Next.js API路由特点：**
- 文件系统路由（自动路由映射）
- 服务端渲染（SSR）
- 类型安全的参数处理
- 内置错误处理

#### 4.2 类型安全实现

**TypeScript接口定义：**
```typescript
export interface User {
  id: number;
  name: string;
  email: string;
  createdAt: string;
}
```

**API路由类型处理：**
- Next.js 15的Promise参数处理
- 请求体类型验证
- 响应类型定义

### 第五阶段：前端组件开发

#### 5.1 组件架构

**组件分层：**
- **页面级组件** (`page.tsx`) - 业务逻辑协调
- **功能组件** (`UserList.tsx`, `UserForm.tsx`) - 具体功能实现
- **UI组件** (`Button`, `Card`, `Input`) - 基础UI元素

#### 5.2 状态管理

**React状态管理：**
- `useState` - 本地状态管理
- `useEffect` - 副作用处理
- 组件间通信（props回调）

#### 5.3 表单处理

**表单验证：**
- 必填字段验证
- 邮箱格式验证
- 错误提示显示

### 第六阶段：样式和用户体验

#### 6.1 Tailwind CSS应用
- 响应式网格布局
- 组件样式统一
- 交互状态样式

#### 6.2 用户体验优化
- 加载状态显示
- 空数据提示
- 确认删除对话框
- 错误边界处理

## 🔧 开发工作流

### 初始化数据库
```bash
npm run db:init
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 📊 技术特点总结

### 1. 全链路类型安全
- TypeScript从数据库到UI全覆盖
- 编译时错误检测
- IDE智能提示支持

### 2. 现代开发体验
- 热重载开发
- 模块化代码组织
- 组件化架构

### 3. 生产就绪
- 数据库连接池
- 错误处理机制
- 性能优化
- 可扩展架构

### 4. 开发效率
- 开箱即用的UI组件
- 自动路由生成
- 热重载开发体验

## 🎯 学习要点

### 核心技术概念
1. **Next.js App Router** - 现代React全栈开发
2. **TypeScript** - 类型驱动开发
3. **Tailwind CSS** - 实用优先的CSS框架
4. **MySQL** - 关系型数据库操作
5. **API设计** - RESTful接口开发

### 最佳实践
1. **代码组织** - 清晰的目录结构
2. **类型定义** - 接口优先设计
3. **错误处理** - 全面的异常捕获
4. **性能优化** - 数据库连接池
5. **用户体验** - 加载状态和空状态处理

## 🚀 下一步学习方向

1. **添加用户认证** - JWT或Session
2. **实现分页** - 大数据集处理
3. **添加搜索** - 数据库查询优化
4. **表单验证增强** - Zod或React Hook Form
5. **部署配置** - Vercel或Docker

---

**项目地址**: http://localhost:3000
**数据库**: MySQL (root/123123@localhost:3306)
**技术栈**: Next.js 15 + TypeScript + Tailwind CSS v4 + shadcn/ui + MySQL